<wxs src="./index.wxs" module="utils" />

<t-navbar class="nav-bar" title="{{ name }}" left-arrow />

<view class="chat-container">
  <scroll-view class="content" scroll-y scroll-into-view="{{ anchor }}">
    <view class="messages">
      <block wx:for="{{ messages }}" wx:key="index">
        <view wx:if="{{ index === 0 || item.time - messages[index - 1].time > 120000 }}" class="time" >
          {{ utils.formatTime(item.time) }}
        </view>

        <view wx:if="{{ item.from === 0 }}" class="message-area">
          <view class="message self">
          <text space="nbsp">{{ item.content }}</text>
          <t-loading
            wx:if="{{ item.messageId === null }}"
            t-class="loading"
            theme="spinner"
            size="32rpx"
            class="wrapper"
          />
          </view>
          <t-avatar image="{{ myAvatar }}" size="small" />
        </view>

        <view wx:else class="message-area">
          <t-avatar image="{{ avatar }}" size="small" />
          <view class="message other">
          <text space="nbsp">{{ item.content }}</text>
          </view>
        </view>
      </block>
      <view id="bottom" />
    </view>
  </scroll-view>
</view>

<view class="block" style="margin-bottom: {{ keyboardHeight }}px" />
<view class="bottom" style="margin-bottom: {{ keyboardHeight }}px">
  <view class="input">
    <input
      value="{{ input }}"
      type="text"
      confirm-type="send"
      placeholder="请输入"
      placeholder-style="color: #00000066"
      adjust-position="{{ false }}"
      hold-keyboard
      confirm-hold
      bindkeyboardheightchange="handleKeyboardHeightChange"
      bindblur="handleBlur"
      bindinput="handleInput"
      bindconfirm="sendMessage"
    />
  </view>
  <t-button class="send" theme="primary" shape="round" disabled="{{ !input }}" bind:tap="sendMessage">发送</t-button>
</view>
