/** 将数字补足2位 */
function formatNum(num) {
  return num < 10 ? '0' + num : num;
}

/** 格式化时间 */
function formatTime(time) {
  var date = getDate(time);
  var now = getDate();
  var Y = date.getFullYear(),
    M = date.getMonth() + 1,
    D = date.getDate(),
    h = date.getHours(),
    m = date.getMinutes();
  var Y0 = now.getFullYear(),
    M0 = now.getMonth() + 1,
    D0 = now.getDate();
  if (Y === Y0) {
    if (M === M0 && D === D0) return '今天 ' + formatNum(h) + ':' + formatNum(m);
    if (M === M0 && D === D0 - 1) return '昨天 ' + formatNum(h) + ':' + formatNum(m);
    return M + '月' + D + '日 ' + formatNum(h) + ':' + formatNum(m);
  }
  return Y + '年' + M + '月' + D + '日 ' + formatNum(h) + ':' + formatNum(m);
}

module.exports = {
  formatTime: formatTime,
};
