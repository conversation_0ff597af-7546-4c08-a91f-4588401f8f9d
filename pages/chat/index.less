/* pages/chat/index.wxss */
.chat-container {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  height: 100vh;
  font-size: 32rpx;
  background-color: #fff;
}

.nav-bar {
  border-bottom: 1rpx solid #e7e7e7;
}

.content {
  height: 0;
  flex-grow: 1;
}

.messages {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
  padding: 32rpx 24rpx;
}

.time {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  height: 56rpx;
  color: #00000066;
  font-size: 24rpx;
  line-height: 40rpx;
}

.message-area {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.message {
  position: relative;
  box-sizing: border-box;
  max-width: 510rpx;
  padding: 24rpx;
  font-size: 28rpx;
  line-height: 44rpx;
}

.message.self {
  border-radius: 24rpx 0 24rpx 24rpx;
  margin-left: auto;
  background-color: #d9e1ff;
}

.message.other {
  border-radius: 0 24rpx 24rpx 24rpx;
  background-color: #f3f3f3;
}

.loading {
  position: absolute;
  right: calc(100% + 16rpx);
  top: 50%;
  transform: translateY(-50%);
}

.block {
  height: calc(env(safe-area-inset-bottom) + 129rpx);
}

.bottom {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 24rpx;
  padding: 24rpx 24rpx calc(env(safe-area-inset-bottom) + 24rpx);
  border-top: 1rpx solid #e7e7e7;
  background-color: #ffffff;
}

.input {
  box-sizing: border-box;
  flex-grow: 1;
  height: 80rpx;
  padding: 16rpx 32rpx;
  border-radius: 40rpx;
  border: 1rpx solid #dcdcdc;
  background: #f3f3f3;
  line-height: 48rpx;
}

.input > input {
  width: 100%;
  height: 48rpx;
  line-height: 48rpx;
  margin-bottom: 40rpx;
}

.send {
  width: 128rpx !important;
  margin: 0;
  font-weight: normal !important;
}
