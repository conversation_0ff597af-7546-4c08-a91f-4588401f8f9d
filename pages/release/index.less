/* pages/release/index.wxss */
.release-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 0;
  box-sizing: border-box;

  .box {
    width: 100%;
  }
  .upload {
    height: 112rpx * 2;
    position: relative;
    &-class {
      width: 344rpx * 2;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  .desc {
    height: 132rpx * 2;
    display: flex;
    padding: 32rpx;
    align-items: flex-start;
    justify-content: space-between;
    box-sizing: border-box;
    .desc-class {
      width: 246rpx * 2;
      height: 100%;
      padding: 0;
    }
  }
  .taggroup {
    .cell-title-class {
      width: 81rpx * 2;
    }
    .cell-note-class {
      overflow-x: auto;
      justify-content: start;
    }
    .tag-class {
      margin-left: 24rpx;
    }
  }
  .btngroup {
    display: flex;
    justify-content: space-evenly;
    padding: 32rpx;
    position: fixed;
    bottom: 64rpx;
    .btn-class {
      width: 163.5rpx * 2;
      margin: 0;
    }
  }
}
