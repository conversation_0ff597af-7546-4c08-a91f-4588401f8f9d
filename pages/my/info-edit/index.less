page {
  background-color: #fff;
}

.info-edit {
  --td-input-vertical-padding: 0;
}

.info-edit__cell {
  .t-cell__title {
    flex: none;
    width: 162rpx;
  }
}

.info-edit__gender {
  --td-radio-vertical-padding: 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.info-edit__introduction {
  width: 100%;
  padding: 0 !important;
  height: 200rpx;
}

.info-edit__photos {
  width: 100%;
}

.info-edit__save {
  position: fixed;
  left: 32rpx;
  right: 32rpx;
  bottom: calc(env(safe-area-inset-bottom) + 32rpx);
}
