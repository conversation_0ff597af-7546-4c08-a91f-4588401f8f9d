<wxs src="./index.wxs" module="utils" />

<nav nav-type="title" title-text="全部消息"></nav>
<scroll-view
  class="message-list"
  scroll-y
  refresher-enabled
  refresher-triggered="{{ loading }}"
  bindrefresherrefresh="getMessageList"
>
  <t-cell
    wx:for="{{ messageList }}"
    wx:key="index"
    t-class-image="avatar"
    t-class-description="content {{ utils.isRead(item.messages) ? '' : 'unread' }}"
    image="{{ item.avatar }}"
    title="{{ item.name }}"
    description="{{ item.messages[item.messages.length - 1].content }}"
    hover
    bind:tap="toChat"
    data-user-id="{{ item.userId }}"
  >
    <t-badge slot="right-icon" count="{{ utils.computeUnreadNum(item.messages) }}" class="wrapper" />
  </t-cell>
</scroll-view>
