<view class="search">
  <t-search placeholder="输入tdesign，有预览结果" resultList="{{resultList}}" bind:change="onChangeValue" />
</view>
<t-dropdown-menu>
  <t-dropdown-item options="{{product.options}}" placement="right" value="{{product.value}}" bindchange="onChange" />
  <t-dropdown-item options="{{sorter.options}}" placement="right" default-value="{{sorter.value}}" />
</t-dropdown-menu>
<scroll-view class="list" scroll-y="true" scroll-with-animation="true" enable-back-to-top="true" bindscrolltolower="loadMore">
  <block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index">
    <t-swipe-cell>
      <t-cell
        bordered="{{false}}"
        title="{{item.name}}"
        description="一段很长很长的内容文字"
        note="辅助信息"
        image="https://tdesign.gtimg.com/mobile/demos/avatar1.png"
      />
      <view slot="right" class="btn-wrapper">
        <view class="btn edit-btn" bind:tap="onEdit">编辑</view>
        <view class="btn delete-btn" bind:tap="onDelete">删除</view>
      </view>
    </t-swipe-cell>
  </block>
  <end isEnd="{{isEnd}}"></end>
</scroll-view>
  


