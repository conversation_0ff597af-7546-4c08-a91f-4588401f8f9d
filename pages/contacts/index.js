// pages/contacts/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    product: {
      value: 'all',
      options: [
        {
          value: 'all',
          label: '所有关系',
        },
        {
          value: 'new',
          label: '亲戚',
        },
        {
          value: 'hot',
          label: '同学',
        },
        {
          value: 'new1',
          label: '同事',
        }
      ],
    },
    sorter: {
      value: 'default',
      options: [
        {
          value: 'default',
          label: '金额大小',
        },
        {
          value: 'price',
          label: '来往笔数',
        },
        {
          value: 'diff',
          label: '差值升序',
        },
        {
          value: 'diff',
          label: '差值降序',
        },
        {
          value: 'diff',
          label: '姓名升序',
        },
      ],
    },
    list: new Array(30).fill(10).map((_, i) => ({
      name: `联系人${i}`,
      desc: `描述信息${i}`,
      price: i,
      diff: i,
    })),
    isEnd:true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  loadMore() {
    // this.setData({
    //   list: this.data.list.concat(new Array(30).fill(10).map((_, i) => ({
    //     name: `联系人${i}`,
    //     desc: `描述信息${i}`,
    //     price: i,
    //     diff: i,
    //   }))),
    // });
  },

  onChangeValue(e) {
    const { value } = e.detail;
    const list = [
      'tdesign-vue',
      'tdesign-react',
      'tdesign-miniprogram',
      'tdesign-angular',
      'tdesign-mobile-vue',
      'tdesign-mobile-react',
    ];
    this.setData({
      resultList: value ? list.filter((v) => v.includes(value)) : [],
    });
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})