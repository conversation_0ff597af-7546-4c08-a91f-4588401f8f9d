{"name": "tdesign-miniprogram-starter", "version": "0.0.2", "lockfileVersion": 2, "requires": true, "packages": {"": {"name": "tdesign-miniprogram-starter", "version": "0.0.2", "license": "MIT", "dependencies": {"tdesign-miniprogram": "^1.8.6"}, "devDependencies": {"eslint": "^8.49.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-prettier": "^5.0.0", "husky": "^8.0.3", "lint-staged": "^14.0.1", "prettier": "^3.0.2"}}, "node_modules/@aashutoshrathi/word-wrap": {"version": "1.2.6", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/@eslint-community/eslint-utils": {"version": "4.4.0", "dev": true, "license": "MIT", "dependencies": {"eslint-visitor-keys": "^3.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}}, "node_modules/@eslint-community/regexpp": {"version": "4.8.0", "dev": true, "license": "MIT", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}}, "node_modules/@eslint/eslintrc": {"version": "2.1.2", "dev": true, "license": "MIT", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.6.0", "globals": "^13.19.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint/js": {"version": "8.49.0", "resolved": "https://registry.npmjs.org/@eslint/js/-/js-8.49.0.tgz", "integrity": "sha512-1S8uAY/MTJqVx0SC4epBq+N2yhuwtNwLbJYNZyhL2pO1ZVKn5HFXav5T41Ryzy9K9V7ZId2JB2oy/W4aCd9/2w==", "dev": true, "license": "MIT", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "node_modules/@humanwhocodes/config-array": {"version": "0.11.11", "resolved": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.11.11.tgz", "integrity": "sha512-N2brEuAadi0CcdeMXUkhbZB84eskAc8MEX1By6qEchoVywSgXPIjou4rYsl0V3Hj0ZnuGycGCjdNgockbzeWNA==", "dev": true, "license": "Apache-2.0", "dependencies": {"@humanwhocodes/object-schema": "^1.2.1", "debug": "^4.1.1", "minimatch": "^3.0.5"}, "engines": {"node": ">=10.10.0"}}, "node_modules/@humanwhocodes/module-importer": {"version": "1.0.1", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=12.22"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/object-schema": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-1.2.1.tgz", "integrity": "sha512-ZnQMnLV4e7hDlUvw8H+U8ASL02SS2Gn6+9Ac3wGGLIe7+je2AeAOxPY+izIPJDfFDb7eDjev0Us8MO1iFRN8hA==", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@pkgr/utils": {"version": "2.4.2", "dev": true, "license": "MIT", "dependencies": {"cross-spawn": "^7.0.3", "fast-glob": "^3.3.0", "is-glob": "^4.0.3", "open": "^9.1.0", "picocolors": "^1.0.0", "tslib": "^2.6.0"}, "engines": {"node": "^12.20.0 || ^14.18.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/unts"}}, "node_modules/@types/json5": {"version": "0.0.29", "dev": true, "license": "MIT"}, "node_modules/acorn": {"version": "8.10.0", "dev": true, "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-jsx": {"version": "5.3.2", "dev": true, "license": "MIT", "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/ajv": {"version": "6.12.6", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ansi-escapes": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"type-fest": "^1.0.2"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ansi-escapes/node_modules/type-fest": {"version": "1.4.0", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ansi-regex": {"version": "5.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/argparse": {"version": "2.0.1", "dev": true, "license": "Python-2.0"}, "node_modules/array-buffer-byte-length": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "is-array-buffer": "^3.0.1"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array-includes": {"version": "3.1.7", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "es-abstract": "^1.22.1", "get-intrinsic": "^1.2.1", "is-string": "^1.0.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array.prototype.findlastindex": {"version": "1.2.3", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "es-abstract": "^1.22.1", "es-shim-unscopables": "^1.0.0", "get-intrinsic": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array.prototype.flat": {"version": "1.3.1", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4", "es-shim-unscopables": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array.prototype.flatmap": {"version": "1.3.1", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4", "es-shim-unscopables": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/arraybuffer.prototype.slice": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"array-buffer-byte-length": "^1.0.0", "call-bind": "^1.0.2", "define-properties": "^1.2.0", "get-intrinsic": "^1.2.1", "is-array-buffer": "^3.0.2", "is-shared-array-buffer": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/available-typed-arrays": {"version": "1.0.5", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/balanced-match": {"version": "1.0.2", "dev": true, "license": "MIT"}, "node_modules/big-integer": {"version": "1.6.51", "dev": true, "license": "Unlicense", "engines": {"node": ">=0.6"}}, "node_modules/bplist-parser": {"version": "0.2.0", "dev": true, "license": "MIT", "dependencies": {"big-integer": "^1.6.44"}, "engines": {"node": ">= 5.10.0"}}, "node_modules/brace-expansion": {"version": "1.1.11", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "3.0.2", "dev": true, "license": "MIT", "dependencies": {"fill-range": "^7.0.1"}, "engines": {"node": ">=8"}}, "node_modules/bundle-name": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"run-applescript": "^5.0.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/call-bind": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/callsites": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/cli-cursor": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"restore-cursor": "^4.0.0"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cli-truncate": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"slice-ansi": "^5.0.0", "string-width": "^5.0.0"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/colorette": {"version": "2.0.20", "dev": true, "license": "MIT"}, "node_modules/commander": {"version": "11.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=16"}}, "node_modules/concat-map": {"version": "0.0.1", "dev": true, "license": "MIT"}, "node_modules/confusing-browser-globals": {"version": "1.0.11", "dev": true, "license": "MIT"}, "node_modules/cross-spawn": {"version": "7.0.3", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/dayjs": {"version": "1.11.9", "license": "MIT"}, "node_modules/debug": {"version": "4.3.4", "dev": true, "license": "MIT", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/deep-is": {"version": "0.1.4", "dev": true, "license": "MIT"}, "node_modules/default-browser": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"bundle-name": "^3.0.0", "default-browser-id": "^3.0.0", "execa": "^7.1.1", "titleize": "^3.0.0"}, "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/default-browser-id": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"bplist-parser": "^0.2.0", "untildify": "^4.0.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/define-lazy-prop": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/define-properties": {"version": "1.2.0", "dev": true, "license": "MIT", "dependencies": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/doctrine": {"version": "3.0.0", "dev": true, "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/eastasianwidth": {"version": "0.2.0", "dev": true, "license": "MIT"}, "node_modules/emoji-regex": {"version": "9.2.2", "dev": true, "license": "MIT"}, "node_modules/es-abstract": {"version": "1.22.1", "dev": true, "license": "MIT", "dependencies": {"array-buffer-byte-length": "^1.0.0", "arraybuffer.prototype.slice": "^1.0.1", "available-typed-arrays": "^1.0.5", "call-bind": "^1.0.2", "es-set-tostringtag": "^2.0.1", "es-to-primitive": "^1.2.1", "function.prototype.name": "^1.1.5", "get-intrinsic": "^1.2.1", "get-symbol-description": "^1.0.0", "globalthis": "^1.0.3", "gopd": "^1.0.1", "has": "^1.0.3", "has-property-descriptors": "^1.0.0", "has-proto": "^1.0.1", "has-symbols": "^1.0.3", "internal-slot": "^1.0.5", "is-array-buffer": "^3.0.2", "is-callable": "^1.2.7", "is-negative-zero": "^2.0.2", "is-regex": "^1.1.4", "is-shared-array-buffer": "^1.0.2", "is-string": "^1.0.7", "is-typed-array": "^1.1.10", "is-weakref": "^1.0.2", "object-inspect": "^1.12.3", "object-keys": "^1.1.1", "object.assign": "^4.1.4", "regexp.prototype.flags": "^1.5.0", "safe-array-concat": "^1.0.0", "safe-regex-test": "^1.0.0", "string.prototype.trim": "^1.2.7", "string.prototype.trimend": "^1.0.6", "string.prototype.trimstart": "^1.0.6", "typed-array-buffer": "^1.0.0", "typed-array-byte-length": "^1.0.0", "typed-array-byte-offset": "^1.0.0", "typed-array-length": "^1.0.4", "unbox-primitive": "^1.0.2", "which-typed-array": "^1.1.10"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/es-set-tostringtag": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"get-intrinsic": "^1.1.3", "has": "^1.0.3", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-shim-unscopables": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"has": "^1.0.3"}}, "node_modules/es-to-primitive": {"version": "1.2.1", "dev": true, "license": "MIT", "dependencies": {"is-callable": "^1.1.4", "is-date-object": "^1.0.1", "is-symbol": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/escape-string-regexp": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint": {"version": "8.49.0", "resolved": "https://registry.npmjs.org/eslint/-/eslint-8.49.0.tgz", "integrity": "sha512-jw03ENfm6VJI0jA9U+8H5zfl5b+FvuU3YYvZRdZHOlU2ggJkxrlkJH4HcDrZpj6YwD8kuYqvQM8LyesoazrSOQ==", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.6.1", "@eslint/eslintrc": "^2.1.2", "@eslint/js": "8.49.0", "@humanwhocodes/config-array": "^0.11.11", "@humanwhocodes/module-importer": "^1.0.1", "@nodelib/fs.walk": "^1.2.8", "ajv": "^6.12.4", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.3.2", "doctrine": "^3.0.0", "escape-string-regexp": "^4.0.0", "eslint-scope": "^7.2.2", "eslint-visitor-keys": "^3.4.3", "espree": "^9.6.1", "esquery": "^1.4.2", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^6.0.1", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "globals": "^13.19.0", "graphemer": "^1.4.0", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "is-path-inside": "^3.0.3", "js-yaml": "^4.1.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3", "strip-ansi": "^6.0.1", "text-table": "^0.2.0"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-config-airbnb-base": {"version": "15.0.0", "dev": true, "license": "MIT", "dependencies": {"confusing-browser-globals": "^1.0.10", "object.assign": "^4.1.2", "object.entries": "^1.1.5", "semver": "^6.3.0"}, "engines": {"node": "^10.12.0 || >=12.0.0"}, "peerDependencies": {"eslint": "^7.32.0 || ^8.2.0", "eslint-plugin-import": "^2.25.2"}}, "node_modules/eslint-config-prettier": {"version": "9.0.0", "dev": true, "license": "MIT", "bin": {"eslint-config-prettier": "bin/cli.js"}, "peerDependencies": {"eslint": ">=7.0.0"}}, "node_modules/eslint-import-resolver-node": {"version": "0.3.9", "dev": true, "license": "MIT", "dependencies": {"debug": "^3.2.7", "is-core-module": "^2.13.0", "resolve": "^1.22.4"}}, "node_modules/eslint-import-resolver-node/node_modules/debug": {"version": "3.2.7", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/eslint-module-utils": {"version": "2.8.0", "dev": true, "license": "MIT", "dependencies": {"debug": "^3.2.7"}, "engines": {"node": ">=4"}, "peerDependenciesMeta": {"eslint": {"optional": true}}}, "node_modules/eslint-module-utils/node_modules/debug": {"version": "3.2.7", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/eslint-plugin-import": {"version": "2.28.1", "dev": true, "license": "MIT", "dependencies": {"array-includes": "^3.1.6", "array.prototype.findlastindex": "^1.2.2", "array.prototype.flat": "^1.3.1", "array.prototype.flatmap": "^1.3.1", "debug": "^3.2.7", "doctrine": "^2.1.0", "eslint-import-resolver-node": "^0.3.7", "eslint-module-utils": "^2.8.0", "has": "^1.0.3", "is-core-module": "^2.13.0", "is-glob": "^4.0.3", "minimatch": "^3.1.2", "object.fromentries": "^2.0.6", "object.groupby": "^1.0.0", "object.values": "^1.1.6", "semver": "^6.3.1", "tsconfig-paths": "^3.14.2"}, "engines": {"node": ">=4"}, "peerDependencies": {"eslint": "^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8"}}, "node_modules/eslint-plugin-import/node_modules/debug": {"version": "3.2.7", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/eslint-plugin-import/node_modules/doctrine": {"version": "2.1.0", "dev": true, "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/eslint-plugin-prettier": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"prettier-linter-helpers": "^1.0.0", "synckit": "^0.8.5"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/prettier"}, "peerDependencies": {"@types/eslint": ">=8.0.0", "eslint": ">=8.0.0", "prettier": ">=3.0.0"}, "peerDependenciesMeta": {"@types/eslint": {"optional": true}, "eslint-config-prettier": {"optional": true}}}, "node_modules/eslint-scope": {"version": "7.2.2", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-visitor-keys": {"version": "3.4.3", "dev": true, "license": "Apache-2.0", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/espree": {"version": "9.6.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^8.9.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.4.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/esquery": {"version": "1.5.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.1.0"}, "engines": {"node": ">=0.10"}}, "node_modules/esrecurse": {"version": "4.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "5.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/esutils": {"version": "2.0.3", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/eventemitter3": {"version": "5.0.1", "dev": true, "license": "MIT"}, "node_modules/execa": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.1", "human-signals": "^4.3.0", "is-stream": "^3.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^5.1.0", "onetime": "^6.0.0", "signal-exit": "^3.0.7", "strip-final-newline": "^3.0.0"}, "engines": {"node": "^14.18.0 || ^16.14.0 || >=18.0.0"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/fast-deep-equal": {"version": "3.1.3", "dev": true, "license": "MIT"}, "node_modules/fast-diff": {"version": "1.3.0", "dev": true, "license": "Apache-2.0"}, "node_modules/fast-glob": {"version": "3.3.1", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.4"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fast-glob/node_modules/glob-parent": {"version": "5.1.2", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "dev": true, "license": "MIT"}, "node_modules/fast-levenshtein": {"version": "2.0.6", "dev": true, "license": "MIT"}, "node_modules/fastq": {"version": "1.15.0", "dev": true, "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "node_modules/file-entry-cache": {"version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"flat-cache": "^3.0.4"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/fill-range": {"version": "7.0.1", "dev": true, "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/find-up": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/flat-cache": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"flatted": "^3.2.7", "keyv": "^4.5.3", "rimraf": "^3.0.2"}, "engines": {"node": ">=12.0.0"}}, "node_modules/flatted": {"version": "3.2.7", "dev": true, "license": "ISC"}, "node_modules/for-each": {"version": "0.3.3", "dev": true, "license": "MIT", "dependencies": {"is-callable": "^1.1.3"}}, "node_modules/fs.realpath": {"version": "1.0.0", "dev": true, "license": "ISC"}, "node_modules/function-bind": {"version": "1.1.1", "dev": true, "license": "MIT"}, "node_modules/function.prototype.name": {"version": "1.1.6", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "es-abstract": "^1.22.1", "functions-have-names": "^1.2.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/functions-have-names": {"version": "1.2.3", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-intrinsic": {"version": "1.2.1", "dev": true, "license": "MIT", "dependencies": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-proto": "^1.0.1", "has-symbols": "^1.0.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-stream": {"version": "6.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/get-symbol-description": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/glob": {"version": "7.2.3", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "6.0.2", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "node_modules/globals": {"version": "13.21.0", "dev": true, "license": "MIT", "dependencies": {"type-fest": "^0.20.2"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/globalthis": {"version": "1.0.3", "dev": true, "license": "MIT", "dependencies": {"define-properties": "^1.1.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gopd": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"get-intrinsic": "^1.1.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/graphemer": {"version": "1.4.0", "dev": true, "license": "MIT"}, "node_modules/has": {"version": "1.0.3", "dev": true, "license": "MIT", "dependencies": {"function-bind": "^1.1.1"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/has-bigints": {"version": "1.0.2", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/has-property-descriptors": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"get-intrinsic": "^1.1.1"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-proto": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.0.3", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"has-symbols": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/human-signals": {"version": "4.3.1", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=14.18.0"}}, "node_modules/husky": {"version": "8.0.3", "dev": true, "license": "MIT", "bin": {"husky": "lib/bin.js"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/typicode"}}, "node_modules/ignore": {"version": "5.2.4", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/import-fresh": {"version": "3.3.0", "dev": true, "license": "MIT", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/imurmurhash": {"version": "0.1.4", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/inflight": {"version": "1.0.6", "dev": true, "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "dev": true, "license": "ISC"}, "node_modules/internal-slot": {"version": "1.0.5", "dev": true, "license": "MIT", "dependencies": {"get-intrinsic": "^1.2.0", "has": "^1.0.3", "side-channel": "^1.0.4"}, "engines": {"node": ">= 0.4"}}, "node_modules/is-array-buffer": {"version": "3.0.2", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.2.0", "is-typed-array": "^1.1.10"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-bigint": {"version": "1.0.4", "dev": true, "license": "MIT", "dependencies": {"has-bigints": "^1.0.1"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-boolean-object": {"version": "1.1.2", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-callable": {"version": "1.2.7", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-core-module": {"version": "2.13.0", "dev": true, "license": "MIT", "dependencies": {"has": "^1.0.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-date-object": {"version": "1.0.5", "dev": true, "license": "MIT", "dependencies": {"has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-docker": {"version": "3.0.0", "dev": true, "license": "MIT", "bin": {"is-docker": "cli.js"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-extglob": {"version": "2.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-glob": {"version": "4.0.3", "dev": true, "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-inside-container": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"is-docker": "^3.0.0"}, "bin": {"is-inside-container": "cli.js"}, "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-negative-zero": {"version": "2.0.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-number": {"version": "7.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-number-object": {"version": "1.0.7", "dev": true, "license": "MIT", "dependencies": {"has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-path-inside": {"version": "3.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-regex": {"version": "1.1.4", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-shared-array-buffer": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-stream": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-string": {"version": "1.0.7", "dev": true, "license": "MIT", "dependencies": {"has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-symbol": {"version": "1.0.4", "dev": true, "license": "MIT", "dependencies": {"has-symbols": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-typed-array": {"version": "1.1.12", "dev": true, "license": "MIT", "dependencies": {"which-typed-array": "^1.1.11"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-weakref": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-wsl": {"version": "2.2.0", "dev": true, "license": "MIT", "dependencies": {"is-docker": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/is-wsl/node_modules/is-docker": {"version": "2.2.1", "dev": true, "license": "MIT", "bin": {"is-docker": "cli.js"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/isarray": {"version": "2.0.5", "dev": true, "license": "MIT"}, "node_modules/isexe": {"version": "2.0.0", "dev": true, "license": "ISC"}, "node_modules/js-yaml": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/json-buffer": {"version": "3.0.1", "dev": true, "license": "MIT"}, "node_modules/json-schema-traverse": {"version": "0.4.1", "dev": true, "license": "MIT"}, "node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/json5": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"minimist": "^1.2.0"}, "bin": {"json5": "lib/cli.js"}}, "node_modules/keyv": {"version": "4.5.3", "dev": true, "license": "MIT", "dependencies": {"json-buffer": "3.0.1"}}, "node_modules/levn": {"version": "0.4.1", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/lilconfig": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/lint-staged": {"version": "14.0.1", "dev": true, "license": "MIT", "dependencies": {"chalk": "5.3.0", "commander": "11.0.0", "debug": "4.3.4", "execa": "7.2.0", "lilconfig": "2.1.0", "listr2": "6.6.1", "micromatch": "4.0.5", "pidtree": "0.6.0", "string-argv": "0.3.2", "yaml": "2.3.1"}, "bin": {"lint-staged": "bin/lint-staged.js"}, "engines": {"node": "^16.14.0 || >=18.0.0"}, "funding": {"url": "https://opencollective.com/lint-staged"}}, "node_modules/lint-staged/node_modules/chalk": {"version": "5.3.0", "dev": true, "license": "MIT", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/listr2": {"version": "6.6.1", "dev": true, "license": "MIT", "dependencies": {"cli-truncate": "^3.1.0", "colorette": "^2.0.20", "eventemitter3": "^5.0.1", "log-update": "^5.0.1", "rfdc": "^1.3.0", "wrap-ansi": "^8.1.0"}, "engines": {"node": ">=16.0.0"}, "peerDependencies": {"enquirer": ">= 2.3.0 < 3"}, "peerDependenciesMeta": {"enquirer": {"optional": true}}}, "node_modules/locate-path": {"version": "6.0.0", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lodash.merge": {"version": "4.6.2", "dev": true, "license": "MIT"}, "node_modules/log-update": {"version": "5.0.1", "dev": true, "license": "MIT", "dependencies": {"ansi-escapes": "^5.0.0", "cli-cursor": "^4.0.0", "slice-ansi": "^5.0.0", "strip-ansi": "^7.0.1", "wrap-ansi": "^8.0.1"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/log-update/node_modules/ansi-regex": {"version": "6.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/log-update/node_modules/strip-ansi": {"version": "7.1.0", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/merge-stream": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/merge2": {"version": "1.4.1", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/micromatch": {"version": "4.0.5", "dev": true, "license": "MIT", "dependencies": {"braces": "^3.0.2", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mimic-fn": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/minimatch": {"version": "3.1.2", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.8", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/ms": {"version": "2.1.2", "dev": true, "license": "MIT"}, "node_modules/natural-compare": {"version": "1.4.0", "dev": true, "license": "MIT"}, "node_modules/npm-run-path": {"version": "5.1.0", "dev": true, "license": "MIT", "dependencies": {"path-key": "^4.0.0"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/npm-run-path/node_modules/path-key": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/object-inspect": {"version": "1.12.3", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-keys": {"version": "1.1.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/object.assign": {"version": "4.1.4", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "has-symbols": "^1.0.3", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object.entries": {"version": "1.1.7", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "es-abstract": "^1.22.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/object.fromentries": {"version": "2.0.7", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "es-abstract": "^1.22.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object.groupby": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "es-abstract": "^1.22.1", "get-intrinsic": "^1.2.1"}}, "node_modules/object.values": {"version": "1.1.7", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "es-abstract": "^1.22.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/once": {"version": "1.4.0", "dev": true, "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/onetime": {"version": "6.0.0", "dev": true, "license": "MIT", "dependencies": {"mimic-fn": "^4.0.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/open": {"version": "9.1.0", "dev": true, "license": "MIT", "dependencies": {"default-browser": "^4.0.0", "define-lazy-prop": "^3.0.0", "is-inside-container": "^1.0.0", "is-wsl": "^2.2.0"}, "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/optionator": {"version": "0.9.3", "dev": true, "license": "MIT", "dependencies": {"@aashutoshrathi/word-wrap": "^1.2.3", "deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/p-limit": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/parent-module": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/path-exists": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-key": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-parse": {"version": "1.0.7", "dev": true, "license": "MIT"}, "node_modules/picocolors": {"version": "1.0.0", "dev": true, "license": "ISC"}, "node_modules/picomatch": {"version": "2.3.1", "dev": true, "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pidtree": {"version": "0.6.0", "dev": true, "license": "MIT", "bin": {"pidtree": "bin/pidtree.js"}, "engines": {"node": ">=0.10"}}, "node_modules/prelude-ls": {"version": "1.2.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "node_modules/prettier": {"version": "3.0.3", "dev": true, "license": "MIT", "bin": {"prettier": "bin/prettier.cjs"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/prettier/prettier?sponsor=1"}}, "node_modules/prettier-linter-helpers": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"fast-diff": "^1.1.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/punycode": {"version": "2.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/queue-microtask": {"version": "1.2.3", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/regexp.prototype.flags": {"version": "1.5.0", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "functions-have-names": "^1.2.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve": {"version": "1.22.4", "dev": true, "license": "MIT", "dependencies": {"is-core-module": "^2.13.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-from": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/restore-cursor": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/restore-cursor/node_modules/mimic-fn": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/restore-cursor/node_modules/onetime": {"version": "5.1.2", "dev": true, "license": "MIT", "dependencies": {"mimic-fn": "^2.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/reusify": {"version": "1.0.4", "dev": true, "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/rfdc": {"version": "1.3.0", "dev": true, "license": "MIT"}, "node_modules/rimraf": {"version": "3.0.2", "dev": true, "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/run-applescript": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"execa": "^5.0.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/run-applescript/node_modules/execa": {"version": "5.1.1", "dev": true, "license": "MIT", "dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/run-applescript/node_modules/human-signals": {"version": "2.1.0", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=10.17.0"}}, "node_modules/run-applescript/node_modules/is-stream": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/run-applescript/node_modules/mimic-fn": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/run-applescript/node_modules/npm-run-path": {"version": "4.0.1", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/run-applescript/node_modules/onetime": {"version": "5.1.2", "dev": true, "license": "MIT", "dependencies": {"mimic-fn": "^2.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/run-applescript/node_modules/strip-final-newline": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/run-parallel": {"version": "1.2.0", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/safe-array-concat": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.2.0", "has-symbols": "^1.0.3", "isarray": "^2.0.5"}, "engines": {"node": ">=0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safe-regex-test": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.3", "is-regex": "^1.1.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/semver": {"version": "6.3.1", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/shebang-command": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/side-channel": {"version": "1.0.4", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.0", "get-intrinsic": "^1.0.2", "object-inspect": "^1.9.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/signal-exit": {"version": "3.0.7", "dev": true, "license": "ISC"}, "node_modules/slice-ansi": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^6.0.0", "is-fullwidth-code-point": "^4.0.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/slice-ansi?sponsor=1"}}, "node_modules/slice-ansi/node_modules/ansi-styles": {"version": "6.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/string-argv": {"version": "0.3.2", "dev": true, "license": "MIT", "engines": {"node": ">=0.6.19"}}, "node_modules/string-width": {"version": "5.1.2", "dev": true, "license": "MIT", "dependencies": {"eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/string-width/node_modules/ansi-regex": {"version": "6.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/string-width/node_modules/strip-ansi": {"version": "7.1.0", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/string.prototype.trim": {"version": "1.2.7", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.trimend": {"version": "1.0.6", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.trimstart": {"version": "1.0.7", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "es-abstract": "^1.22.1"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/strip-ansi": {"version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-bom": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/strip-final-newline": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/strip-json-comments": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/synckit": {"version": "0.8.5", "dev": true, "license": "MIT", "dependencies": {"@pkgr/utils": "^2.3.1", "tslib": "^2.5.0"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/unts"}}, "node_modules/tdesign-miniprogram": {"version": "1.8.6", "resolved": "https://mirrors.tencent.com/npm/tdesign-miniprogram/-/tdesign-miniprogram-1.8.6.tgz", "integrity": "sha512-FbV6/1Evfc5qoy7NQPuLwsrnAGytAAbKkPN/eaA02Qe5YxBgsJlYM7oXI5rSiinjjOC6T/wbGTFurEH/ASd+dQ==", "license": "MIT", "dependencies": {"dayjs": "^1.10.7", "tinycolor2": "^1.4.2"}}, "node_modules/text-table": {"version": "0.2.0", "dev": true, "license": "MIT"}, "node_modules/tinycolor2": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/tinycolor2/-/tinycolor2-1.6.0.tgz", "integrity": "sha512-XPaBkWQJdsf3pLKJV9p4qN/S+fm2Oj8AIPo1BTUhg5oxkvm9+SVEGFdhyOz7tTdUTfvxMiAs4sp6/eZO2Ew+pw=="}, "node_modules/titleize": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/to-regex-range": {"version": "5.0.1", "dev": true, "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/tsconfig-paths": {"version": "3.14.2", "dev": true, "license": "MIT", "dependencies": {"@types/json5": "^0.0.29", "json5": "^1.0.2", "minimist": "^1.2.6", "strip-bom": "^3.0.0"}}, "node_modules/tslib": {"version": "2.6.2", "dev": true, "license": "0BSD"}, "node_modules/type-check": {"version": "0.4.0", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/type-fest": {"version": "0.20.2", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/typed-array-buffer": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.2.1", "is-typed-array": "^1.1.10"}, "engines": {"node": ">= 0.4"}}, "node_modules/typed-array-byte-length": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "for-each": "^0.3.3", "has-proto": "^1.0.1", "is-typed-array": "^1.1.10"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typed-array-byte-offset": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.5", "call-bind": "^1.0.2", "for-each": "^0.3.3", "has-proto": "^1.0.1", "is-typed-array": "^1.1.10"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typed-array-length": {"version": "1.0.4", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "for-each": "^0.3.3", "is-typed-array": "^1.1.9"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/unbox-primitive": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "has-bigints": "^1.0.2", "has-symbols": "^1.0.3", "which-boxed-primitive": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/untildify": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/uri-js": {"version": "4.4.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/which": {"version": "2.0.2", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/which-boxed-primitive": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"is-bigint": "^1.0.1", "is-boolean-object": "^1.1.0", "is-number-object": "^1.0.4", "is-string": "^1.0.5", "is-symbol": "^1.0.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/which-typed-array": {"version": "1.1.11", "dev": true, "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.5", "call-bind": "^1.0.2", "for-each": "^0.3.3", "gopd": "^1.0.1", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/wrap-ansi": {"version": "8.1.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^6.1.0", "string-width": "^5.0.1", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrap-ansi/node_modules/ansi-regex": {"version": "6.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/wrap-ansi/node_modules/ansi-styles": {"version": "6.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/wrap-ansi/node_modules/strip-ansi": {"version": "7.1.0", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/wrappy": {"version": "1.0.2", "dev": true, "license": "ISC"}, "node_modules/yaml": {"version": "2.3.1", "dev": true, "license": "ISC", "engines": {"node": ">= 14"}}, "node_modules/yocto-queue": {"version": "0.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}}, "dependencies": {"@aashutoshrathi/word-wrap": {"version": "1.2.6", "dev": true}, "@eslint-community/eslint-utils": {"version": "4.4.0", "dev": true, "requires": {"eslint-visitor-keys": "^3.3.0"}}, "@eslint-community/regexpp": {"version": "4.8.0", "dev": true}, "@eslint/eslintrc": {"version": "2.1.2", "dev": true, "requires": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.6.0", "globals": "^13.19.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}}, "@eslint/js": {"version": "8.49.0", "resolved": "https://registry.npmjs.org/@eslint/js/-/js-8.49.0.tgz", "integrity": "sha512-1S8uAY/MTJqVx0SC4epBq+N2yhuwtNwLbJYNZyhL2pO1ZVKn5HFXav5T41Ryzy9K9V7ZId2JB2oy/W4aCd9/2w==", "dev": true}, "@humanwhocodes/config-array": {"version": "0.11.11", "resolved": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.11.11.tgz", "integrity": "sha512-N2brEuAadi0CcdeMXUkhbZB84eskAc8MEX1By6qEchoVywSgXPIjou4rYsl0V3Hj0ZnuGycGCjdNgockbzeWNA==", "dev": true, "requires": {"@humanwhocodes/object-schema": "^1.2.1", "debug": "^4.1.1", "minimatch": "^3.0.5"}}, "@humanwhocodes/module-importer": {"version": "1.0.1", "dev": true}, "@humanwhocodes/object-schema": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-1.2.1.tgz", "integrity": "sha512-ZnQMnLV4e7hDlUvw8H+U8ASL02SS2Gn6+9Ac3wGGLIe7+je2AeAOxPY+izIPJDfFDb7eDjev0Us8MO1iFRN8hA==", "dev": true}, "@nodelib/fs.scandir": {"version": "2.1.5", "dev": true, "requires": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}}, "@nodelib/fs.stat": {"version": "2.0.5", "dev": true}, "@nodelib/fs.walk": {"version": "1.2.8", "dev": true, "requires": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}}, "@pkgr/utils": {"version": "2.4.2", "dev": true, "requires": {"cross-spawn": "^7.0.3", "fast-glob": "^3.3.0", "is-glob": "^4.0.3", "open": "^9.1.0", "picocolors": "^1.0.0", "tslib": "^2.6.0"}}, "@types/json5": {"version": "0.0.29", "dev": true}, "acorn": {"version": "8.10.0", "dev": true}, "acorn-jsx": {"version": "5.3.2", "dev": true, "requires": {}}, "ajv": {"version": "6.12.6", "dev": true, "requires": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "ansi-escapes": {"version": "5.0.0", "dev": true, "requires": {"type-fest": "^1.0.2"}, "dependencies": {"type-fest": {"version": "1.4.0", "dev": true}}}, "ansi-regex": {"version": "5.0.1", "dev": true}, "ansi-styles": {"version": "4.3.0", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "argparse": {"version": "2.0.1", "dev": true}, "array-buffer-byte-length": {"version": "1.0.0", "dev": true, "requires": {"call-bind": "^1.0.2", "is-array-buffer": "^3.0.1"}}, "array-includes": {"version": "3.1.7", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "es-abstract": "^1.22.1", "get-intrinsic": "^1.2.1", "is-string": "^1.0.7"}}, "array.prototype.findlastindex": {"version": "1.2.3", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "es-abstract": "^1.22.1", "es-shim-unscopables": "^1.0.0", "get-intrinsic": "^1.2.1"}}, "array.prototype.flat": {"version": "1.3.1", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4", "es-shim-unscopables": "^1.0.0"}}, "array.prototype.flatmap": {"version": "1.3.1", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4", "es-shim-unscopables": "^1.0.0"}}, "arraybuffer.prototype.slice": {"version": "1.0.1", "dev": true, "requires": {"array-buffer-byte-length": "^1.0.0", "call-bind": "^1.0.2", "define-properties": "^1.2.0", "get-intrinsic": "^1.2.1", "is-array-buffer": "^3.0.2", "is-shared-array-buffer": "^1.0.2"}}, "available-typed-arrays": {"version": "1.0.5", "dev": true}, "balanced-match": {"version": "1.0.2", "dev": true}, "big-integer": {"version": "1.6.51", "dev": true}, "bplist-parser": {"version": "0.2.0", "dev": true, "requires": {"big-integer": "^1.6.44"}}, "brace-expansion": {"version": "1.1.11", "dev": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "braces": {"version": "3.0.2", "dev": true, "requires": {"fill-range": "^7.0.1"}}, "bundle-name": {"version": "3.0.0", "dev": true, "requires": {"run-applescript": "^5.0.0"}}, "call-bind": {"version": "1.0.2", "dev": true, "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}}, "callsites": {"version": "3.1.0", "dev": true}, "chalk": {"version": "4.1.2", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "cli-cursor": {"version": "4.0.0", "dev": true, "requires": {"restore-cursor": "^4.0.0"}}, "cli-truncate": {"version": "3.1.0", "dev": true, "requires": {"slice-ansi": "^5.0.0", "string-width": "^5.0.0"}}, "color-convert": {"version": "2.0.1", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "dev": true}, "colorette": {"version": "2.0.20", "dev": true}, "commander": {"version": "11.0.0", "dev": true}, "concat-map": {"version": "0.0.1", "dev": true}, "confusing-browser-globals": {"version": "1.0.11", "dev": true}, "cross-spawn": {"version": "7.0.3", "dev": true, "requires": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}}, "dayjs": {"version": "1.11.9"}, "debug": {"version": "4.3.4", "dev": true, "requires": {"ms": "2.1.2"}}, "deep-is": {"version": "0.1.4", "dev": true}, "default-browser": {"version": "4.0.0", "dev": true, "requires": {"bundle-name": "^3.0.0", "default-browser-id": "^3.0.0", "execa": "^7.1.1", "titleize": "^3.0.0"}}, "default-browser-id": {"version": "3.0.0", "dev": true, "requires": {"bplist-parser": "^0.2.0", "untildify": "^4.0.0"}}, "define-lazy-prop": {"version": "3.0.0", "dev": true}, "define-properties": {"version": "1.2.0", "dev": true, "requires": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}}, "doctrine": {"version": "3.0.0", "dev": true, "requires": {"esutils": "^2.0.2"}}, "eastasianwidth": {"version": "0.2.0", "dev": true}, "emoji-regex": {"version": "9.2.2", "dev": true}, "es-abstract": {"version": "1.22.1", "dev": true, "requires": {"array-buffer-byte-length": "^1.0.0", "arraybuffer.prototype.slice": "^1.0.1", "available-typed-arrays": "^1.0.5", "call-bind": "^1.0.2", "es-set-tostringtag": "^2.0.1", "es-to-primitive": "^1.2.1", "function.prototype.name": "^1.1.5", "get-intrinsic": "^1.2.1", "get-symbol-description": "^1.0.0", "globalthis": "^1.0.3", "gopd": "^1.0.1", "has": "^1.0.3", "has-property-descriptors": "^1.0.0", "has-proto": "^1.0.1", "has-symbols": "^1.0.3", "internal-slot": "^1.0.5", "is-array-buffer": "^3.0.2", "is-callable": "^1.2.7", "is-negative-zero": "^2.0.2", "is-regex": "^1.1.4", "is-shared-array-buffer": "^1.0.2", "is-string": "^1.0.7", "is-typed-array": "^1.1.10", "is-weakref": "^1.0.2", "object-inspect": "^1.12.3", "object-keys": "^1.1.1", "object.assign": "^4.1.4", "regexp.prototype.flags": "^1.5.0", "safe-array-concat": "^1.0.0", "safe-regex-test": "^1.0.0", "string.prototype.trim": "^1.2.7", "string.prototype.trimend": "^1.0.6", "string.prototype.trimstart": "^1.0.6", "typed-array-buffer": "^1.0.0", "typed-array-byte-length": "^1.0.0", "typed-array-byte-offset": "^1.0.0", "typed-array-length": "^1.0.4", "unbox-primitive": "^1.0.2", "which-typed-array": "^1.1.10"}}, "es-set-tostringtag": {"version": "2.0.1", "dev": true, "requires": {"get-intrinsic": "^1.1.3", "has": "^1.0.3", "has-tostringtag": "^1.0.0"}}, "es-shim-unscopables": {"version": "1.0.0", "dev": true, "requires": {"has": "^1.0.3"}}, "es-to-primitive": {"version": "1.2.1", "dev": true, "requires": {"is-callable": "^1.1.4", "is-date-object": "^1.0.1", "is-symbol": "^1.0.2"}}, "escape-string-regexp": {"version": "4.0.0", "dev": true}, "eslint": {"version": "8.49.0", "resolved": "https://registry.npmjs.org/eslint/-/eslint-8.49.0.tgz", "integrity": "sha512-jw03ENfm6VJI0jA9U+8H5zfl5b+FvuU3YYvZRdZHOlU2ggJkxrlkJH4HcDrZpj6YwD8kuYqvQM8LyesoazrSOQ==", "dev": true, "requires": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.6.1", "@eslint/eslintrc": "^2.1.2", "@eslint/js": "8.49.0", "@humanwhocodes/config-array": "^0.11.11", "@humanwhocodes/module-importer": "^1.0.1", "@nodelib/fs.walk": "^1.2.8", "ajv": "^6.12.4", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.3.2", "doctrine": "^3.0.0", "escape-string-regexp": "^4.0.0", "eslint-scope": "^7.2.2", "eslint-visitor-keys": "^3.4.3", "espree": "^9.6.1", "esquery": "^1.4.2", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^6.0.1", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "globals": "^13.19.0", "graphemer": "^1.4.0", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "is-path-inside": "^3.0.3", "js-yaml": "^4.1.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3", "strip-ansi": "^6.0.1", "text-table": "^0.2.0"}}, "eslint-config-airbnb-base": {"version": "15.0.0", "dev": true, "requires": {"confusing-browser-globals": "^1.0.10", "object.assign": "^4.1.2", "object.entries": "^1.1.5", "semver": "^6.3.0"}}, "eslint-config-prettier": {"version": "9.0.0", "dev": true, "requires": {}}, "eslint-import-resolver-node": {"version": "0.3.9", "dev": true, "requires": {"debug": "^3.2.7", "is-core-module": "^2.13.0", "resolve": "^1.22.4"}, "dependencies": {"debug": {"version": "3.2.7", "dev": true, "requires": {"ms": "^2.1.1"}}}}, "eslint-module-utils": {"version": "2.8.0", "dev": true, "requires": {"debug": "^3.2.7"}, "dependencies": {"debug": {"version": "3.2.7", "dev": true, "requires": {"ms": "^2.1.1"}}}}, "eslint-plugin-import": {"version": "2.28.1", "dev": true, "requires": {"array-includes": "^3.1.6", "array.prototype.findlastindex": "^1.2.2", "array.prototype.flat": "^1.3.1", "array.prototype.flatmap": "^1.3.1", "debug": "^3.2.7", "doctrine": "^2.1.0", "eslint-import-resolver-node": "^0.3.7", "eslint-module-utils": "^2.8.0", "has": "^1.0.3", "is-core-module": "^2.13.0", "is-glob": "^4.0.3", "minimatch": "^3.1.2", "object.fromentries": "^2.0.6", "object.groupby": "^1.0.0", "object.values": "^1.1.6", "semver": "^6.3.1", "tsconfig-paths": "^3.14.2"}, "dependencies": {"debug": {"version": "3.2.7", "dev": true, "requires": {"ms": "^2.1.1"}}, "doctrine": {"version": "2.1.0", "dev": true, "requires": {"esutils": "^2.0.2"}}}}, "eslint-plugin-prettier": {"version": "5.0.0", "dev": true, "requires": {"prettier-linter-helpers": "^1.0.0", "synckit": "^0.8.5"}}, "eslint-scope": {"version": "7.2.2", "dev": true, "requires": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}}, "eslint-visitor-keys": {"version": "3.4.3", "dev": true}, "espree": {"version": "9.6.1", "dev": true, "requires": {"acorn": "^8.9.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.4.1"}}, "esquery": {"version": "1.5.0", "dev": true, "requires": {"estraverse": "^5.1.0"}}, "esrecurse": {"version": "4.3.0", "dev": true, "requires": {"estraverse": "^5.2.0"}}, "estraverse": {"version": "5.3.0", "dev": true}, "esutils": {"version": "2.0.3", "dev": true}, "eventemitter3": {"version": "5.0.1", "dev": true}, "execa": {"version": "7.2.0", "dev": true, "requires": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.1", "human-signals": "^4.3.0", "is-stream": "^3.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^5.1.0", "onetime": "^6.0.0", "signal-exit": "^3.0.7", "strip-final-newline": "^3.0.0"}}, "fast-deep-equal": {"version": "3.1.3", "dev": true}, "fast-diff": {"version": "1.3.0", "dev": true}, "fast-glob": {"version": "3.3.1", "dev": true, "requires": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.4"}, "dependencies": {"glob-parent": {"version": "5.1.2", "dev": true, "requires": {"is-glob": "^4.0.1"}}}}, "fast-json-stable-stringify": {"version": "2.1.0", "dev": true}, "fast-levenshtein": {"version": "2.0.6", "dev": true}, "fastq": {"version": "1.15.0", "dev": true, "requires": {"reusify": "^1.0.4"}}, "file-entry-cache": {"version": "6.0.1", "dev": true, "requires": {"flat-cache": "^3.0.4"}}, "fill-range": {"version": "7.0.1", "dev": true, "requires": {"to-regex-range": "^5.0.1"}}, "find-up": {"version": "5.0.0", "dev": true, "requires": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}}, "flat-cache": {"version": "3.1.0", "dev": true, "requires": {"flatted": "^3.2.7", "keyv": "^4.5.3", "rimraf": "^3.0.2"}}, "flatted": {"version": "3.2.7", "dev": true}, "for-each": {"version": "0.3.3", "dev": true, "requires": {"is-callable": "^1.1.3"}}, "fs.realpath": {"version": "1.0.0", "dev": true}, "function-bind": {"version": "1.1.1", "dev": true}, "function.prototype.name": {"version": "1.1.6", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "es-abstract": "^1.22.1", "functions-have-names": "^1.2.3"}}, "functions-have-names": {"version": "1.2.3", "dev": true}, "get-intrinsic": {"version": "1.2.1", "dev": true, "requires": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-proto": "^1.0.1", "has-symbols": "^1.0.3"}}, "get-stream": {"version": "6.0.1", "dev": true}, "get-symbol-description": {"version": "1.0.0", "dev": true, "requires": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.1"}}, "glob": {"version": "7.2.3", "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "glob-parent": {"version": "6.0.2", "dev": true, "requires": {"is-glob": "^4.0.3"}}, "globals": {"version": "13.21.0", "dev": true, "requires": {"type-fest": "^0.20.2"}}, "globalthis": {"version": "1.0.3", "dev": true, "requires": {"define-properties": "^1.1.3"}}, "gopd": {"version": "1.0.1", "dev": true, "requires": {"get-intrinsic": "^1.1.3"}}, "graphemer": {"version": "1.4.0", "dev": true}, "has": {"version": "1.0.3", "dev": true, "requires": {"function-bind": "^1.1.1"}}, "has-bigints": {"version": "1.0.2", "dev": true}, "has-flag": {"version": "4.0.0", "dev": true}, "has-property-descriptors": {"version": "1.0.0", "dev": true, "requires": {"get-intrinsic": "^1.1.1"}}, "has-proto": {"version": "1.0.1", "dev": true}, "has-symbols": {"version": "1.0.3", "dev": true}, "has-tostringtag": {"version": "1.0.0", "dev": true, "requires": {"has-symbols": "^1.0.2"}}, "human-signals": {"version": "4.3.1", "dev": true}, "husky": {"version": "8.0.3", "dev": true}, "ignore": {"version": "5.2.4", "dev": true}, "import-fresh": {"version": "3.3.0", "dev": true, "requires": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}}, "imurmurhash": {"version": "0.1.4", "dev": true}, "inflight": {"version": "1.0.6", "dev": true, "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.4", "dev": true}, "internal-slot": {"version": "1.0.5", "dev": true, "requires": {"get-intrinsic": "^1.2.0", "has": "^1.0.3", "side-channel": "^1.0.4"}}, "is-array-buffer": {"version": "3.0.2", "dev": true, "requires": {"call-bind": "^1.0.2", "get-intrinsic": "^1.2.0", "is-typed-array": "^1.1.10"}}, "is-bigint": {"version": "1.0.4", "dev": true, "requires": {"has-bigints": "^1.0.1"}}, "is-boolean-object": {"version": "1.1.2", "dev": true, "requires": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}}, "is-callable": {"version": "1.2.7", "dev": true}, "is-core-module": {"version": "2.13.0", "dev": true, "requires": {"has": "^1.0.3"}}, "is-date-object": {"version": "1.0.5", "dev": true, "requires": {"has-tostringtag": "^1.0.0"}}, "is-docker": {"version": "3.0.0", "dev": true}, "is-extglob": {"version": "2.1.1", "dev": true}, "is-fullwidth-code-point": {"version": "4.0.0", "dev": true}, "is-glob": {"version": "4.0.3", "dev": true, "requires": {"is-extglob": "^2.1.1"}}, "is-inside-container": {"version": "1.0.0", "dev": true, "requires": {"is-docker": "^3.0.0"}}, "is-negative-zero": {"version": "2.0.2", "dev": true}, "is-number": {"version": "7.0.0", "dev": true}, "is-number-object": {"version": "1.0.7", "dev": true, "requires": {"has-tostringtag": "^1.0.0"}}, "is-path-inside": {"version": "3.0.3", "dev": true}, "is-regex": {"version": "1.1.4", "dev": true, "requires": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}}, "is-shared-array-buffer": {"version": "1.0.2", "dev": true, "requires": {"call-bind": "^1.0.2"}}, "is-stream": {"version": "3.0.0", "dev": true}, "is-string": {"version": "1.0.7", "dev": true, "requires": {"has-tostringtag": "^1.0.0"}}, "is-symbol": {"version": "1.0.4", "dev": true, "requires": {"has-symbols": "^1.0.2"}}, "is-typed-array": {"version": "1.1.12", "dev": true, "requires": {"which-typed-array": "^1.1.11"}}, "is-weakref": {"version": "1.0.2", "dev": true, "requires": {"call-bind": "^1.0.2"}}, "is-wsl": {"version": "2.2.0", "dev": true, "requires": {"is-docker": "^2.0.0"}, "dependencies": {"is-docker": {"version": "2.2.1", "dev": true}}}, "isarray": {"version": "2.0.5", "dev": true}, "isexe": {"version": "2.0.0", "dev": true}, "js-yaml": {"version": "4.1.0", "dev": true, "requires": {"argparse": "^2.0.1"}}, "json-buffer": {"version": "3.0.1", "dev": true}, "json-schema-traverse": {"version": "0.4.1", "dev": true}, "json-stable-stringify-without-jsonify": {"version": "1.0.1", "dev": true}, "json5": {"version": "1.0.2", "dev": true, "requires": {"minimist": "^1.2.0"}}, "keyv": {"version": "4.5.3", "dev": true, "requires": {"json-buffer": "3.0.1"}}, "levn": {"version": "0.4.1", "dev": true, "requires": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}}, "lilconfig": {"version": "2.1.0", "dev": true}, "lint-staged": {"version": "14.0.1", "dev": true, "requires": {"chalk": "5.3.0", "commander": "11.0.0", "debug": "4.3.4", "execa": "7.2.0", "lilconfig": "2.1.0", "listr2": "6.6.1", "micromatch": "4.0.5", "pidtree": "0.6.0", "string-argv": "0.3.2", "yaml": "2.3.1"}, "dependencies": {"chalk": {"version": "5.3.0", "dev": true}}}, "listr2": {"version": "6.6.1", "dev": true, "requires": {"cli-truncate": "^3.1.0", "colorette": "^2.0.20", "eventemitter3": "^5.0.1", "log-update": "^5.0.1", "rfdc": "^1.3.0", "wrap-ansi": "^8.1.0"}}, "locate-path": {"version": "6.0.0", "dev": true, "requires": {"p-locate": "^5.0.0"}}, "lodash.merge": {"version": "4.6.2", "dev": true}, "log-update": {"version": "5.0.1", "dev": true, "requires": {"ansi-escapes": "^5.0.0", "cli-cursor": "^4.0.0", "slice-ansi": "^5.0.0", "strip-ansi": "^7.0.1", "wrap-ansi": "^8.0.1"}, "dependencies": {"ansi-regex": {"version": "6.0.1", "dev": true}, "strip-ansi": {"version": "7.1.0", "dev": true, "requires": {"ansi-regex": "^6.0.1"}}}}, "merge-stream": {"version": "2.0.0", "dev": true}, "merge2": {"version": "1.4.1", "dev": true}, "micromatch": {"version": "4.0.5", "dev": true, "requires": {"braces": "^3.0.2", "picomatch": "^2.3.1"}}, "mimic-fn": {"version": "4.0.0", "dev": true}, "minimatch": {"version": "3.1.2", "dev": true, "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "1.2.8", "dev": true}, "ms": {"version": "2.1.2", "dev": true}, "natural-compare": {"version": "1.4.0", "dev": true}, "npm-run-path": {"version": "5.1.0", "dev": true, "requires": {"path-key": "^4.0.0"}, "dependencies": {"path-key": {"version": "4.0.0", "dev": true}}}, "object-inspect": {"version": "1.12.3", "dev": true}, "object-keys": {"version": "1.1.1", "dev": true}, "object.assign": {"version": "4.1.4", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "has-symbols": "^1.0.3", "object-keys": "^1.1.1"}}, "object.entries": {"version": "1.1.7", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "es-abstract": "^1.22.1"}}, "object.fromentries": {"version": "2.0.7", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "es-abstract": "^1.22.1"}}, "object.groupby": {"version": "1.0.1", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "es-abstract": "^1.22.1", "get-intrinsic": "^1.2.1"}}, "object.values": {"version": "1.1.7", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "es-abstract": "^1.22.1"}}, "once": {"version": "1.4.0", "dev": true, "requires": {"wrappy": "1"}}, "onetime": {"version": "6.0.0", "dev": true, "requires": {"mimic-fn": "^4.0.0"}}, "open": {"version": "9.1.0", "dev": true, "requires": {"default-browser": "^4.0.0", "define-lazy-prop": "^3.0.0", "is-inside-container": "^1.0.0", "is-wsl": "^2.2.0"}}, "optionator": {"version": "0.9.3", "dev": true, "requires": {"@aashutoshrathi/word-wrap": "^1.2.3", "deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0"}}, "p-limit": {"version": "3.1.0", "dev": true, "requires": {"yocto-queue": "^0.1.0"}}, "p-locate": {"version": "5.0.0", "dev": true, "requires": {"p-limit": "^3.0.2"}}, "parent-module": {"version": "1.0.1", "dev": true, "requires": {"callsites": "^3.0.0"}}, "path-exists": {"version": "4.0.0", "dev": true}, "path-is-absolute": {"version": "1.0.1", "dev": true}, "path-key": {"version": "3.1.1", "dev": true}, "path-parse": {"version": "1.0.7", "dev": true}, "picocolors": {"version": "1.0.0", "dev": true}, "picomatch": {"version": "2.3.1", "dev": true}, "pidtree": {"version": "0.6.0", "dev": true}, "prelude-ls": {"version": "1.2.1", "dev": true}, "prettier": {"version": "3.0.3", "dev": true}, "prettier-linter-helpers": {"version": "1.0.0", "dev": true, "requires": {"fast-diff": "^1.1.2"}}, "punycode": {"version": "2.3.0", "dev": true}, "queue-microtask": {"version": "1.2.3", "dev": true}, "regexp.prototype.flags": {"version": "1.5.0", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "functions-have-names": "^1.2.3"}}, "resolve": {"version": "1.22.4", "dev": true, "requires": {"is-core-module": "^2.13.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}}, "resolve-from": {"version": "4.0.0", "dev": true}, "restore-cursor": {"version": "4.0.0", "dev": true, "requires": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}, "dependencies": {"mimic-fn": {"version": "2.1.0", "dev": true}, "onetime": {"version": "5.1.2", "dev": true, "requires": {"mimic-fn": "^2.1.0"}}}}, "reusify": {"version": "1.0.4", "dev": true}, "rfdc": {"version": "1.3.0", "dev": true}, "rimraf": {"version": "3.0.2", "dev": true, "requires": {"glob": "^7.1.3"}}, "run-applescript": {"version": "5.0.0", "dev": true, "requires": {"execa": "^5.0.0"}, "dependencies": {"execa": {"version": "5.1.1", "dev": true, "requires": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}}, "human-signals": {"version": "2.1.0", "dev": true}, "is-stream": {"version": "2.0.1", "dev": true}, "mimic-fn": {"version": "2.1.0", "dev": true}, "npm-run-path": {"version": "4.0.1", "dev": true, "requires": {"path-key": "^3.0.0"}}, "onetime": {"version": "5.1.2", "dev": true, "requires": {"mimic-fn": "^2.1.0"}}, "strip-final-newline": {"version": "2.0.0", "dev": true}}}, "run-parallel": {"version": "1.2.0", "dev": true, "requires": {"queue-microtask": "^1.2.2"}}, "safe-array-concat": {"version": "1.0.0", "dev": true, "requires": {"call-bind": "^1.0.2", "get-intrinsic": "^1.2.0", "has-symbols": "^1.0.3", "isarray": "^2.0.5"}}, "safe-regex-test": {"version": "1.0.0", "dev": true, "requires": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.3", "is-regex": "^1.1.4"}}, "semver": {"version": "6.3.1", "dev": true}, "shebang-command": {"version": "2.0.0", "dev": true, "requires": {"shebang-regex": "^3.0.0"}}, "shebang-regex": {"version": "3.0.0", "dev": true}, "side-channel": {"version": "1.0.4", "dev": true, "requires": {"call-bind": "^1.0.0", "get-intrinsic": "^1.0.2", "object-inspect": "^1.9.0"}}, "signal-exit": {"version": "3.0.7", "dev": true}, "slice-ansi": {"version": "5.0.0", "dev": true, "requires": {"ansi-styles": "^6.0.0", "is-fullwidth-code-point": "^4.0.0"}, "dependencies": {"ansi-styles": {"version": "6.2.1", "dev": true}}}, "string-argv": {"version": "0.3.2", "dev": true}, "string-width": {"version": "5.1.2", "dev": true, "requires": {"eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1"}, "dependencies": {"ansi-regex": {"version": "6.0.1", "dev": true}, "strip-ansi": {"version": "7.1.0", "dev": true, "requires": {"ansi-regex": "^6.0.1"}}}}, "string.prototype.trim": {"version": "1.2.7", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4"}}, "string.prototype.trimend": {"version": "1.0.6", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4"}}, "string.prototype.trimstart": {"version": "1.0.7", "dev": true, "requires": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "es-abstract": "^1.22.1"}}, "strip-ansi": {"version": "6.0.1", "dev": true, "requires": {"ansi-regex": "^5.0.1"}}, "strip-bom": {"version": "3.0.0", "dev": true}, "strip-final-newline": {"version": "3.0.0", "dev": true}, "strip-json-comments": {"version": "3.1.1", "dev": true}, "supports-color": {"version": "7.2.0", "dev": true, "requires": {"has-flag": "^4.0.0"}}, "supports-preserve-symlinks-flag": {"version": "1.0.0", "dev": true}, "synckit": {"version": "0.8.5", "dev": true, "requires": {"@pkgr/utils": "^2.3.1", "tslib": "^2.5.0"}}, "tdesign-miniprogram": {"version": "1.8.6", "resolved": "https://mirrors.tencent.com/npm/tdesign-miniprogram/-/tdesign-miniprogram-1.8.6.tgz", "integrity": "sha512-FbV6/1Evfc5qoy7NQPuLwsrnAGytAAbKkPN/eaA02Qe5YxBgsJlYM7oXI5rSiinjjOC6T/wbGTFurEH/ASd+dQ==", "requires": {"dayjs": "^1.10.7", "tinycolor2": "^1.4.2"}}, "text-table": {"version": "0.2.0", "dev": true}, "tinycolor2": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/tinycolor2/-/tinycolor2-1.6.0.tgz", "integrity": "sha512-XPaBkWQJdsf3pLKJV9p4qN/S+fm2Oj8AIPo1BTUhg5oxkvm9+SVEGFdhyOz7tTdUTfvxMiAs4sp6/eZO2Ew+pw=="}, "titleize": {"version": "3.0.0", "dev": true}, "to-regex-range": {"version": "5.0.1", "dev": true, "requires": {"is-number": "^7.0.0"}}, "tsconfig-paths": {"version": "3.14.2", "dev": true, "requires": {"@types/json5": "^0.0.29", "json5": "^1.0.2", "minimist": "^1.2.6", "strip-bom": "^3.0.0"}}, "tslib": {"version": "2.6.2", "dev": true}, "type-check": {"version": "0.4.0", "dev": true, "requires": {"prelude-ls": "^1.2.1"}}, "type-fest": {"version": "0.20.2", "dev": true}, "typed-array-buffer": {"version": "1.0.0", "dev": true, "requires": {"call-bind": "^1.0.2", "get-intrinsic": "^1.2.1", "is-typed-array": "^1.1.10"}}, "typed-array-byte-length": {"version": "1.0.0", "dev": true, "requires": {"call-bind": "^1.0.2", "for-each": "^0.3.3", "has-proto": "^1.0.1", "is-typed-array": "^1.1.10"}}, "typed-array-byte-offset": {"version": "1.0.0", "dev": true, "requires": {"available-typed-arrays": "^1.0.5", "call-bind": "^1.0.2", "for-each": "^0.3.3", "has-proto": "^1.0.1", "is-typed-array": "^1.1.10"}}, "typed-array-length": {"version": "1.0.4", "dev": true, "requires": {"call-bind": "^1.0.2", "for-each": "^0.3.3", "is-typed-array": "^1.1.9"}}, "unbox-primitive": {"version": "1.0.2", "dev": true, "requires": {"call-bind": "^1.0.2", "has-bigints": "^1.0.2", "has-symbols": "^1.0.3", "which-boxed-primitive": "^1.0.2"}}, "untildify": {"version": "4.0.0", "dev": true}, "uri-js": {"version": "4.4.1", "dev": true, "requires": {"punycode": "^2.1.0"}}, "which": {"version": "2.0.2", "dev": true, "requires": {"isexe": "^2.0.0"}}, "which-boxed-primitive": {"version": "1.0.2", "dev": true, "requires": {"is-bigint": "^1.0.1", "is-boolean-object": "^1.1.0", "is-number-object": "^1.0.4", "is-string": "^1.0.5", "is-symbol": "^1.0.3"}}, "which-typed-array": {"version": "1.1.11", "dev": true, "requires": {"available-typed-arrays": "^1.0.5", "call-bind": "^1.0.2", "for-each": "^0.3.3", "gopd": "^1.0.1", "has-tostringtag": "^1.0.0"}}, "wrap-ansi": {"version": "8.1.0", "dev": true, "requires": {"ansi-styles": "^6.1.0", "string-width": "^5.0.1", "strip-ansi": "^7.0.1"}, "dependencies": {"ansi-regex": {"version": "6.0.1", "dev": true}, "ansi-styles": {"version": "6.2.1", "dev": true}, "strip-ansi": {"version": "7.1.0", "dev": true, "requires": {"ansi-regex": "^6.0.1"}}}}, "wrappy": {"version": "1.0.2", "dev": true}, "yaml": {"version": "2.3.1", "dev": true}, "yocto-queue": {"version": "0.1.0", "dev": true}}}