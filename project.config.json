{"description": "项目配置文件，详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "packOptions": {"ignore": [], "include": []}, "setting": {"bundle": false, "userConfirmedBundleSwitch": false, "urlCheck": true, "scopeDataCheck": false, "coverView": true, "es6": true, "postcss": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "minified": true, "autoAudits": false, "newFeature": false, "uglifyFileName": false, "uploadWithSourceMap": true, "useIsolateContext": true, "nodeModules": false, "enhance": true, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": true, "packNpmManually": false, "enableEngineNative": false, "packNpmRelationList": [], "minifyWXSS": true, "showES6CompileOption": false, "minifyWXML": true, "useStaticServer": true, "checkInvalidKey": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "disableUseStrict": false, "useCompilerPlugins": ["less"], "condition": true, "ignoreUploadUnusedFiles": true, "compileWorklet": false, "localPlugins": false, "swc": false, "disableSWC": true}, "compileType": "miniprogram", "libVersion": "3.7.8", "appid": "wx4d515e32c8a975ac", "projectname": "miniprogram-starter", "condition": {"miniprogram": {"list": [{"name": "login", "pathName": "pages/login/login", "query": "", "scene": null}, {"name": "loginCode", "pathName": "pages/loginCode/loginCode", "query": "", "scene": null}, {"name": "my", "pathName": "pages/my/index", "query": "", "scene": null}, {"name": "info-edit", "pathName": "pages/my/info-edit/index", "query": "", "scene": null}, {"name": "dataCenter", "pathName": "pages/dataCenter/index", "query": "", "scene": null}, {"name": "release", "pathName": "pages/release/index", "query": "", "launchMode": "default", "scene": null}, {"name": "setting", "pathName": "pages/setting/index", "query": "", "launchMode": "default", "scene": null}]}}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "simulatorType": "wechat", "simulatorPluginLibVersion": {"wxext14566970e7e9f62": "3.6.5-39"}, "projectArchitecture": "multiPlatform"}