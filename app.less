/**app.wxss**/
@import '/variable.less';
@import 'miniprogram_npm/tdesign-miniprogram/common/style/theme/_index.wxss'; // 引入暗夜模式主题变量
page {
  background-color: @bg-color;
  --td-brand-color: @td-brand-color; 
  --td-brand-color-hover: @td-brand-color-hover;
  --td-brand-color-active: @td-brand-color-active;
  --td-brand-color-disabled: @td-brand-color-disabled;
  --td-brand-color-focus: @td-brand-color-focus;
  --td-brand-color-light: @td-brand-color-light;
  --td-brand-color-light-active: @td-brand-color-light-active;
}

.page {
  height: 100vh;
  background-color: @bg-color-white;
}
