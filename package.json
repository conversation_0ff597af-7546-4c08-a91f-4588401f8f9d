{"name": "tdesign-miniprogram-starter", "version": "0.0.2", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "lint": "npx eslint ./ --no-eslintrc -c ./.eslintrc.js", "lint:fix": "npm run lint:fix-eslint && npm run lint:fix-prettier", "lint:fix-eslint": "eslint --ext .js,.ts ./ --max-warnings 0 --ignore-path .gitignore --ignore-path .eslintignore --fix", "lint:fix-prettier": "prettier ./ --ignore-path .gitignore --ignore-path .eslint<PERSON>ore --ignore-path .prettierignore --write './**/*.{js,ts}' --ignore-unknown"}, "keywords": ["wechat", "miniprogram", "starter"], "author": "leejim", "license": "MIT", "description": "a start-kit for wechat miniprogram powerby TDesign miniprogram", "dependencies": {"tdesign-miniprogram": "^1.8.6"}, "devDependencies": {"eslint": "^8.49.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-prettier": "^5.0.0", "husky": "^8.0.3", "lint-staged": "^14.0.1", "prettier": "^3.0.2"}, "lint-staged": {"{src,example,script}/**/*.{js,ts,wxml,html,json,less}": ["prettier --write"], "**/*.{js,ts}": ["eslint --fix"]}}